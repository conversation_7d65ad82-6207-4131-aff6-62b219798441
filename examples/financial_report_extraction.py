#!/usr/bin/env python3
"""财务报告信息提取示例"""

import asyncio
import os
from memect_insight_extractor import DocumentExtractor, ExtractionConfig


async def extract_financial_report():
    """提取财务报告信息"""
    
    # 配置提取器
    config = ExtractionConfig(
        llm_base_url=os.getenv("LLM_BASE_URL", "https://api.openai.com/v1"),
        llm_api_key=os.getenv("LLM_API_KEY", "your-api-key-here"),
        llm_model=os.getenv("LLM_MODEL", "gpt-4"),
        llm_temperature=0.1,
        max_retries=3
    )
    
    extractor = DocumentExtractor(config)
    
    # 财务报告文本
    financial_report = """
    ABC科技股份有限公司
    2024年第一季度财务报告
    
    一、主要财务数据
    
    营业收入：本季度实现营业收入12,500万元，同比增长15.2%
    营业成本：营业成本8,200万元，同比增长12.8%
    毛利润：4,300万元，毛利率34.4%
    
    净利润：本季度实现净利润2,150万元，同比增长18.5%
    每股收益：0.86元
    
    二、资产负债情况
    
    总资产：截至2024年3月31日，公司总资产45,600万元
    流动资产：32,800万元，其中货币资金8,500万元
    固定资产：12,800万元
    
    总负债：18,900万元
    流动负债：15,200万元
    长期负债：3,700万元
    
    股东权益：26,700万元
    资产负债率：41.4%
    
    三、现金流量
    
    经营活动现金流量净额：3,200万元
    投资活动现金流量净额：-1,800万元
    筹资活动现金流量净额：-500万元
    
    四、主要财务指标
    
    ROE（净资产收益率）：8.05%
    ROA（总资产收益率）：4.71%
    流动比率：2.16
    速动比率：1.85
    
    五、业务分析
    
    主营业务：软件开发占比65%，系统集成占比25%，技术服务占比10%
    地区分布：华东地区占比45%，华北地区占比30%，其他地区占比25%
    """
    
    # 定义财务报告提取Schema
    schema = {
        "type": "object",
        "properties": {
            "company_info": {
                "type": "object",
                "properties": {
                    "name": {"type": "string", "description": "公司名称"},
                    "report_period": {"type": "string", "description": "报告期间"}
                },
                "description": "公司基本信息"
            },
            "income_statement": {
                "type": "object",
                "properties": {
                    "revenue": {"type": "number", "description": "营业收入（万元）"},
                    "revenue_growth": {"type": "number", "description": "营业收入同比增长率（%）"},
                    "operating_cost": {"type": "number", "description": "营业成本（万元）"},
                    "gross_profit": {"type": "number", "description": "毛利润（万元）"},
                    "gross_margin": {"type": "number", "description": "毛利率（%）"},
                    "net_profit": {"type": "number", "description": "净利润（万元）"},
                    "net_profit_growth": {"type": "number", "description": "净利润同比增长率（%）"},
                    "earnings_per_share": {"type": "number", "description": "每股收益（元）"}
                },
                "description": "损益表数据"
            },
            "balance_sheet": {
                "type": "object",
                "properties": {
                    "total_assets": {"type": "number", "description": "总资产（万元）"},
                    "current_assets": {"type": "number", "description": "流动资产（万元）"},
                    "cash": {"type": "number", "description": "货币资金（万元）"},
                    "fixed_assets": {"type": "number", "description": "固定资产（万元）"},
                    "total_liabilities": {"type": "number", "description": "总负债（万元）"},
                    "current_liabilities": {"type": "number", "description": "流动负债（万元）"},
                    "long_term_liabilities": {"type": "number", "description": "长期负债（万元）"},
                    "shareholders_equity": {"type": "number", "description": "股东权益（万元）"},
                    "debt_ratio": {"type": "number", "description": "资产负债率（%）"}
                },
                "description": "资产负债表数据"
            },
            "cash_flow": {
                "type": "object",
                "properties": {
                    "operating_cash_flow": {"type": "number", "description": "经营活动现金流量净额（万元）"},
                    "investing_cash_flow": {"type": "number", "description": "投资活动现金流量净额（万元）"},
                    "financing_cash_flow": {"type": "number", "description": "筹资活动现金流量净额（万元）"}
                },
                "description": "现金流量表数据"
            },
            "financial_ratios": {
                "type": "object",
                "properties": {
                    "roe": {"type": "number", "description": "净资产收益率（%）"},
                    "roa": {"type": "number", "description": "总资产收益率（%）"},
                    "current_ratio": {"type": "number", "description": "流动比率"},
                    "quick_ratio": {"type": "number", "description": "速动比率"}
                },
                "description": "主要财务指标"
            },
            "business_analysis": {
                "type": "object",
                "properties": {
                    "business_segments": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "segment": {"type": "string", "description": "业务分类"},
                                "percentage": {"type": "number", "description": "占比（%）"}
                            }
                        },
                        "description": "主营业务构成"
                    },
                    "regional_distribution": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "region": {"type": "string", "description": "地区"},
                                "percentage": {"type": "number", "description": "占比（%）"}
                            }
                        },
                        "description": "地区分布"
                    }
                },
                "description": "业务分析"
            }
        },
        "required": ["company_info", "income_statement", "balance_sheet"]
    }
    
    # 自定义提示词
    custom_prompt = """
    你是一个专业的财务分析师，擅长从财务报告中提取关键数据。
    
    请仔细阅读以下财务报告，提取所有相关的财务数据。
    
    注意事项：
    1. 金额数据请保持原单位（万元），不要转换
    2. 百分比数据请只提取数值部分，不包含%符号
    3. 比率数据请保持小数格式
    4. 如果某个数据在报告中没有明确提及，请设置为null
    5. 确保所有数值的准确性，特别是财务指标的计算
    
    请严格按照提供的JSON Schema格式返回结果。
    """
    
    print("🏦 开始提取财务报告信息...")
    print(f"报告长度: {len(financial_report)} 字符")
    print("-" * 60)
    
    try:
        # 执行提取
        result = await extractor.extract(
            text_content=financial_report,
            schema=schema,
            prompt_template=custom_prompt,
            document_id="financial_report_2024_q1"
        )
        
        print(f"提取状态: {result.status}")
        print(f"处理时间: {result.processing_time:.2f} 秒")
        
        if result.is_success():
            print("\n✅ 财务报告提取成功！")
            
            data = result.extracted_data
            
            # 公司信息
            company = data.get("company_info", {})
            print(f"\n📊 公司信息:")
            print(f"  公司名称: {company.get('name')}")
            print(f"  报告期间: {company.get('report_period')}")
            
            # 损益表
            income = data.get("income_statement", {})
            print(f"\n💰 损益表数据:")
            print(f"  营业收入: {income.get('revenue')}万元 (增长{income.get('revenue_growth')}%)")
            print(f"  营业成本: {income.get('operating_cost')}万元")
            print(f"  毛利润: {income.get('gross_profit')}万元 (毛利率{income.get('gross_margin')}%)")
            print(f"  净利润: {income.get('net_profit')}万元 (增长{income.get('net_profit_growth')}%)")
            print(f"  每股收益: {income.get('earnings_per_share')}元")
            
            # 资产负债表
            balance = data.get("balance_sheet", {})
            print(f"\n🏛️ 资产负债表数据:")
            print(f"  总资产: {balance.get('total_assets')}万元")
            print(f"  流动资产: {balance.get('current_assets')}万元")
            print(f"  货币资金: {balance.get('cash')}万元")
            print(f"  总负债: {balance.get('total_liabilities')}万元")
            print(f"  股东权益: {balance.get('shareholders_equity')}万元")
            print(f"  资产负债率: {balance.get('debt_ratio')}%")
            
            # 现金流量
            cash_flow = data.get("cash_flow", {})
            print(f"\n💸 现金流量数据:")
            print(f"  经营活动现金流: {cash_flow.get('operating_cash_flow')}万元")
            print(f"  投资活动现金流: {cash_flow.get('investing_cash_flow')}万元")
            print(f"  筹资活动现金流: {cash_flow.get('financing_cash_flow')}万元")
            
            # 财务指标
            ratios = data.get("financial_ratios", {})
            print(f"\n📈 主要财务指标:")
            print(f"  净资产收益率(ROE): {ratios.get('roe')}%")
            print(f"  总资产收益率(ROA): {ratios.get('roa')}%")
            print(f"  流动比率: {ratios.get('current_ratio')}")
            print(f"  速动比率: {ratios.get('quick_ratio')}")
            
            # 业务分析
            business = data.get("business_analysis", {})
            if business:
                segments = business.get("business_segments", [])
                if segments:
                    print(f"\n🏢 主营业务构成:")
                    for segment in segments:
                        print(f"  {segment.get('segment')}: {segment.get('percentage')}%")
                
                regions = business.get("regional_distribution", [])
                if regions:
                    print(f"\n🌍 地区分布:")
                    for region in regions:
                        print(f"  {region.get('region')}: {region.get('percentage')}%")
        
        else:
            print("\n❌ 财务报告提取失败:")
            for error in result.errors:
                print(f"  - {error.error_type}: {error.error_message}")
        
        # 输出元数据
        if result.metadata:
            print(f"\n📋 处理元数据:")
            for key, value in result.metadata.items():
                print(f"  {key}: {value}")
    
    except Exception as e:
        print(f"❌ 程序异常: {str(e)}")


if __name__ == "__main__":
    # 检查API密钥
    if not os.getenv("LLM_API_KEY") or os.getenv("LLM_API_KEY") == "your-api-key-here":
        print("⚠️ 请设置环境变量 LLM_API_KEY")
        print("示例: export LLM_API_KEY='your-actual-api-key'")
        exit(1)
    
    asyncio.run(extract_financial_report())
