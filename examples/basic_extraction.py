#!/usr/bin/env python3
"""基本提取示例"""

import asyncio
import os
from memect_insight_extractor import DocumentExtractor, ExtractionConfig


async def main():
    """主函数"""
    # 配置提取器
    config = ExtractionConfig(
        llm_base_url=os.getenv("LLM_BASE_URL", "https://api.openai.com/v1"),
        llm_api_key=os.getenv("LLM_API_KEY", "your-api-key-here"),
        llm_model=os.getenv("LLM_MODEL", "gpt-4"),
        llm_temperature=0.1,
        max_retries=3
    )
    
    # 创建提取器
    extractor = DocumentExtractor(config)
    
    # 读取示例文档
    with open("test_data/sample_document.txt", "r", encoding="utf-8") as f:
        text_content = f.read()
    
    # 定义提取Schema
    schema = {
        "type": "object",
        "properties": {
            "contract_number": {
                "type": "string",
                "description": "合同编号"
            },
            "contract_name": {
                "type": "string", 
                "description": "合同名称"
            },
            "party_a": {
                "type": "object",
                "properties": {
                    "company_name": {"type": "string", "description": "公司名称"},
                    "address": {"type": "string", "description": "地址"},
                    "contact_person": {"type": "string", "description": "联系人"},
                    "phone": {"type": "string", "description": "电话"},
                    "email": {"type": "string", "description": "邮箱"}
                },
                "description": "甲方信息"
            },
            "party_b": {
                "type": "object", 
                "properties": {
                    "company_name": {"type": "string", "description": "公司名称"},
                    "address": {"type": "string", "description": "地址"},
                    "contact_person": {"type": "string", "description": "联系人"},
                    "phone": {"type": "string", "description": "电话"},
                    "email": {"type": "string", "description": "邮箱"}
                },
                "description": "乙方信息"
            },
            "contract_amount": {
                "type": "number",
                "description": "合同金额(数字)"
            },
            "project_period": {
                "type": "object",
                "properties": {
                    "start_date": {"type": "string", "description": "开始日期"},
                    "end_date": {"type": "string", "description": "结束日期"}
                },
                "description": "项目周期"
            },
            "signing_date": {
                "type": "string",
                "description": "签署日期"
            },
            "project_content": {
                "type": "array",
                "items": {"type": "string"},
                "description": "项目内容列表"
            }
        },
        "required": ["contract_number", "party_a", "party_b", "contract_amount"]
    }
    
    print("开始提取文档信息...")
    print(f"文档长度: {len(text_content)} 字符")
    print("-" * 50)
    
    try:
        # 执行提取
        result = await extractor.extract(
            text_content=text_content,
            schema=schema
        )
        
        # 输出结果
        print(f"提取状态: {result.status}")
        print(f"处理时间: {result.processing_time:.2f} 秒")
        
        if result.is_success():
            print("\n✅ 提取成功!")
            print("\n提取结果:")
            data = result.extracted_data
            
            print(f"合同编号: {data.get('contract_number')}")
            print(f"合同名称: {data.get('contract_name')}")
            
            if 'party_a' in data:
                party_a = data['party_a']
                print(f"\n甲方信息:")
                print(f"  公司: {party_a.get('company_name')}")
                print(f"  联系人: {party_a.get('contact_person')}")
                print(f"  电话: {party_a.get('phone')}")
                print(f"  邮箱: {party_a.get('email')}")
            
            if 'party_b' in data:
                party_b = data['party_b']
                print(f"\n乙方信息:")
                print(f"  公司: {party_b.get('company_name')}")
                print(f"  联系人: {party_b.get('contact_person')}")
                print(f"  电话: {party_b.get('phone')}")
                print(f"  邮箱: {party_b.get('email')}")
            
            print(f"\n合同金额: ¥{data.get('contract_amount'):,.2f}")
            print(f"签署日期: {data.get('signing_date')}")
            
            if 'project_period' in data:
                period = data['project_period']
                print(f"项目周期: {period.get('start_date')} 至 {period.get('end_date')}")
            
            if 'project_content' in data:
                print(f"\n项目内容:")
                for i, content in enumerate(data['project_content'], 1):
                    print(f"  {i}. {content}")
        
        elif result.status.value == "partial":
            print("\n⚠️ 部分成功，存在一些问题:")
            if result.extracted_data:
                print("已提取的数据:", result.extracted_data)
            
            print("\n错误信息:")
            for error in result.errors:
                print(f"  - {error.error_type}: {error.error_message}")
        
        else:
            print("\n❌ 提取失败:")
            for error in result.errors:
                print(f"  - {error.error_type}: {error.error_message}")
        
        # 输出元数据
        if result.metadata:
            print(f"\n元数据:")
            for key, value in result.metadata.items():
                print(f"  {key}: {value}")
    
    except Exception as e:
        print(f"❌ 程序异常: {str(e)}")


if __name__ == "__main__":
    # 检查API密钥
    if not os.getenv("LLM_API_KEY") or os.getenv("LLM_API_KEY") == "your-api-key-here":
        print("⚠️ 请设置环境变量 LLM_API_KEY")
        print("示例: export LLM_API_KEY='your-actual-api-key'")
        exit(1)
    
    asyncio.run(main())
