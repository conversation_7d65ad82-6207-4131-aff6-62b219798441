#!/usr/bin/env python3
"""批量文档处理示例"""

import asyncio
import os
import json
import time
from typing import List, Dict, Any
from memect_insight_extractor import DocumentExtractor, ExtractionConfig


class BatchProcessor:
    """批量处理器"""
    
    def __init__(self, config: ExtractionConfig):
        self.extractor = DocumentExtractor(config)
        self.results = []
    
    async def process_documents(
        self,
        documents: List[Dict[str, Any]],
        schema: Dict[str, Any],
        max_concurrent: int = 3
    ) -> List[Dict[str, Any]]:
        """批量处理文档
        
        Args:
            documents: 文档列表，每个文档包含id和content
            schema: 提取Schema
            max_concurrent: 最大并发数
            
        Returns:
            处理结果列表
        """
        print(f"📦 开始批量处理 {len(documents)} 个文档...")
        print(f"🔄 最大并发数: {max_concurrent}")
        
        # 创建信号量限制并发数
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_single_document(doc: Dict[str, Any]) -> Dict[str, Any]:
            async with semaphore:
                return await self._process_document(doc, schema)
        
        # 并发处理所有文档
        start_time = time.time()
        tasks = [process_single_document(doc) for doc in documents]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        # 处理结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "document_id": documents[i].get("id", f"doc_{i}"),
                    "success": False,
                    "error": str(result),
                    "data": None,
                    "processing_time": None
                })
            else:
                processed_results.append(result)
        
        # 统计信息
        success_count = sum(1 for r in processed_results if r["success"])
        print(f"\n📊 批量处理完成:")
        print(f"  总文档数: {len(documents)}")
        print(f"  成功数: {success_count}")
        print(f"  失败数: {len(documents) - success_count}")
        print(f"  总耗时: {total_time:.2f} 秒")
        print(f"  平均耗时: {total_time/len(documents):.2f} 秒/文档")
        
        self.results = processed_results
        return processed_results
    
    async def _process_document(
        self,
        document: Dict[str, Any],
        schema: Dict[str, Any]
    ) -> Dict[str, Any]:
        """处理单个文档"""
        doc_id = document.get("id", "unknown")
        content = document.get("content", "")
        
        print(f"🔍 处理文档: {doc_id}")
        
        try:
            result = await self.extractor.extract(
                text_content=content,
                schema=schema,
                document_id=doc_id
            )
            
            return {
                "document_id": doc_id,
                "success": result.is_success(),
                "status": result.status.value,
                "data": result.extracted_data,
                "errors": [e.error_message for e in result.errors],
                "processing_time": result.processing_time,
                "metadata": result.metadata
            }
            
        except Exception as e:
            print(f"❌ 文档 {doc_id} 处理异常: {str(e)}")
            return {
                "document_id": doc_id,
                "success": False,
                "error": str(e),
                "data": None,
                "processing_time": None
            }
    
    def save_results(self, filename: str) -> None:
        """保存结果到文件"""
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        print(f"💾 结果已保存到: {filename}")
    
    def print_summary(self) -> None:
        """打印处理摘要"""
        if not self.results:
            print("📋 暂无处理结果")
            return
        
        successful = [r for r in self.results if r["success"]]
        failed = [r for r in self.results if not r["success"]]
        
        print(f"\n📋 处理摘要:")
        print(f"  成功文档: {len(successful)}")
        print(f"  失败文档: {len(failed)}")
        
        if successful:
            avg_time = sum(r["processing_time"] for r in successful) / len(successful)
            print(f"  平均处理时间: {avg_time:.2f} 秒")
        
        if failed:
            print(f"\n❌ 失败文档:")
            for result in failed[:5]:  # 只显示前5个失败的
                doc_id = result["document_id"]
                error = result.get("error", "未知错误")
                print(f"    {doc_id}: {error}")
            
            if len(failed) > 5:
                print(f"    ... 还有 {len(failed) - 5} 个失败文档")


async def main():
    """主函数"""
    
    # 配置
    config = ExtractionConfig(
        llm_base_url=os.getenv("LLM_BASE_URL", "https://api.openai.com/v1"),
        llm_api_key=os.getenv("LLM_API_KEY", "your-api-key-here"),
        llm_model=os.getenv("LLM_MODEL", "gpt-4"),
        llm_temperature=0.1,
        max_retries=2,
        enable_parallel=True,
        max_parallel_chunks=3
    )
    
    # 创建批量处理器
    processor = BatchProcessor(config)
    
    # 准备测试文档
    documents = [
        {
            "id": "invoice_001",
            "content": """
            发票号码：INV-2024-001
            开票日期：2024年1月15日
            购买方：北京科技有限公司
            销售方：上海软件公司
            商品名称：软件开发服务
            金额：50,000元
            税率：6%
            税额：3,000元
            价税合计：53,000元
            """
        },
        {
            "id": "invoice_002", 
            "content": """
            发票编号：INV-2024-002
            开票时间：2024年1月20日
            客户：深圳贸易有限公司
            供应商：广州制造企业
            货物：电子设备
            不含税金额：80,000元
            增值税：10,400元
            含税总额：90,400元
            """
        },
        {
            "id": "invoice_003",
            "content": """
            票据号：INV-2024-003
            日期：2024年1月25日
            买方：天津物流公司
            卖方：河北生产厂
            产品：工业设备
            单价：25,000元
            数量：2台
            小计：50,000元
            税费：6,500元
            总计：56,500元
            """
        },
        {
            "id": "contract_001",
            "content": """
            合同编号：CON-2024-001
            甲方：杭州互联网公司
            乙方：南京技术服务商
            合同金额：200,000元
            签署日期：2024年2月1日
            服务内容：系统开发与维护
            项目周期：6个月
            """
        },
        {
            "id": "invalid_doc",
            "content": "这是一个无效的文档内容，没有任何有用信息。"
        }
    ]
    
    # 定义通用提取Schema
    schema = {
        "type": "object",
        "properties": {
            "document_type": {
                "type": "string",
                "enum": ["invoice", "contract", "other"],
                "description": "文档类型"
            },
            "document_number": {
                "type": "string",
                "description": "文档编号"
            },
            "date": {
                "type": "string",
                "description": "日期"
            },
            "parties": {
                "type": "array",
                "items": {"type": "string"},
                "description": "相关方（买方、卖方、甲方、乙方等）"
            },
            "amount": {
                "type": "number",
                "description": "主要金额（不含税金额或合同金额）"
            },
            "total_amount": {
                "type": "number", 
                "description": "总金额（含税金额或最终金额）"
            },
            "items": {
                "type": "array",
                "items": {"type": "string"},
                "description": "商品或服务项目"
            }
        },
        "required": ["document_type", "document_number"]
    }
    
    try:
        # 执行批量处理
        results = await processor.process_documents(
            documents=documents,
            schema=schema,
            max_concurrent=2  # 限制并发数避免API限制
        )
        
        # 显示详细结果
        print(f"\n📄 详细结果:")
        for result in results:
            doc_id = result["document_id"]
            success = result["success"]
            
            print(f"\n文档: {doc_id}")
            print(f"状态: {'✅ 成功' if success else '❌ 失败'}")
            
            if success:
                data = result["data"]
                print(f"  文档类型: {data.get('document_type')}")
                print(f"  文档编号: {data.get('document_number')}")
                print(f"  日期: {data.get('date')}")
                print(f"  相关方: {data.get('parties')}")
                print(f"  金额: {data.get('amount')}")
                print(f"  总金额: {data.get('total_amount')}")
                print(f"  处理时间: {result['processing_time']:.2f}秒")
            else:
                errors = result.get("errors", [])
                if errors:
                    print(f"  错误: {errors[0]}")
                else:
                    print(f"  错误: {result.get('error', '未知错误')}")
        
        # 打印摘要
        processor.print_summary()
        
        # 保存结果
        processor.save_results("test_data/batch_results.json")
        
    except Exception as e:
        print(f"❌ 批量处理异常: {str(e)}")


if __name__ == "__main__":
    # 检查API密钥
    if not os.getenv("LLM_API_KEY") or os.getenv("LLM_API_KEY") == "your-api-key-here":
        print("⚠️ 请设置环境变量 LLM_API_KEY")
        print("示例: export LLM_API_KEY='your-actual-api-key'")
        exit(1)
    
    asyncio.run(main())
