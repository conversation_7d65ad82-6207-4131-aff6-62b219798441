#!/usr/bin/env python3
"""简历信息提取示例"""

import asyncio
import os
from memect_insight_extractor import DocumentExtractor, ExtractionConfig


async def extract_resume():
    """提取简历信息"""
    
    # 配置提取器
    config = ExtractionConfig(
        llm_base_url=os.getenv("LLM_BASE_URL", "https://api.openai.com/v1"),
        llm_api_key=os.getenv("LLM_API_KEY", "your-api-key-here"),
        llm_model=os.getenv("LLM_MODEL", "gpt-4"),
        llm_temperature=0.1
    )
    
    extractor = DocumentExtractor(config)
    
    # 简历文本
    resume_text = """
    张伟 - 高级软件工程师
    
    个人信息
    电话：138-1234-5678
    邮箱：<EMAIL>
    地址：北京市海淀区中关村大街123号
    出生日期：1990年5月15日
    
    教育背景
    2012-2016  北京理工大学  计算机科学与技术  本科  GPA: 3.8/4.0
    2016-2018  清华大学  软件工程  硕士  GPA: 3.9/4.0
    
    工作经验
    
    2021.03-至今  腾讯科技有限公司  高级软件工程师
    • 负责微信支付核心系统的开发和维护
    • 参与系统架构设计，提升系统性能30%
    • 带领5人团队完成支付安全模块重构
    • 技术栈：Java, Spring Boot, MySQL, Redis, Kafka
    
    2018.07-2021.02  阿里巴巴集团  软件工程师
    • 参与淘宝商品推荐系统开发
    • 优化推荐算法，提升点击率15%
    • 负责数据处理pipeline的设计和实现
    • 技术栈：Python, TensorFlow, Spark, HBase
    
    2018.03-2018.06  百度公司  实习生
    • 参与搜索引擎相关性算法优化
    • 完成A/B测试平台的前端开发
    • 技术栈：C++, JavaScript, React
    
    项目经验
    
    微信支付风控系统 (2022.01-2022.12)
    • 项目描述：设计和实现实时风控系统，防范支付欺诈
    • 技术难点：处理每秒10万+交易请求，毫秒级响应
    • 主要贡献：设计分布式架构，实现机器学习模型在线推理
    • 项目成果：欺诈检测准确率提升至99.5%，误报率降低50%
    
    淘宝个性化推荐系统 (2019.03-2020.08)
    • 项目描述：基于深度学习的商品推荐系统
    • 技术难点：处理亿级用户行为数据，实时推荐
    • 主要贡献：设计并实现深度神经网络模型
    • 项目成果：推荐点击率提升15%，用户停留时间增加20%
    
    技能专长
    编程语言：Java (精通), Python (精通), C++ (熟练), JavaScript (熟练)
    框架技术：Spring Boot, Django, TensorFlow, React, Vue.js
    数据库：MySQL, Redis, MongoDB, HBase, Elasticsearch
    中间件：Kafka, RabbitMQ, Nginx
    云平台：AWS, 阿里云, 腾讯云
    开发工具：Git, Docker, Kubernetes, Jenkins
    
    证书资格
    • AWS认证解决方案架构师 (2020)
    • Oracle Java SE 8 认证程序员 (2019)
    • PMP项目管理专业人士 (2021)
    
    语言能力
    • 中文：母语
    • 英语：CET-6，能够流利进行技术交流
    • 日语：N2水平，基本交流无障碍
    
    获奖荣誉
    • 2022年腾讯技术突破奖
    • 2020年阿里巴巴优秀员工
    • 2016年清华大学优秀毕业生
    • 2015年ACM-ICPC亚洲区域赛银牌
    
    自我评价
    具有6年软件开发经验，专注于大规模分布式系统设计和机器学习应用。
    具备强烈的技术热情和学习能力，善于解决复杂技术问题。
    具有良好的团队协作能力和项目管理经验。
    """
    
    # 定义简历提取Schema
    schema = {
        "type": "object",
        "properties": {
            "personal_info": {
                "type": "object",
                "properties": {
                    "name": {"type": "string", "description": "姓名"},
                    "phone": {"type": "string", "description": "电话号码"},
                    "email": {"type": "string", "description": "邮箱地址"},
                    "address": {"type": "string", "description": "地址"},
                    "birth_date": {"type": "string", "description": "出生日期"}
                },
                "description": "个人基本信息"
            },
            "education": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "period": {"type": "string", "description": "时间段"},
                        "school": {"type": "string", "description": "学校名称"},
                        "major": {"type": "string", "description": "专业"},
                        "degree": {"type": "string", "description": "学位"},
                        "gpa": {"type": "string", "description": "GPA成绩"}
                    }
                },
                "description": "教育背景"
            },
            "work_experience": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "period": {"type": "string", "description": "工作时间段"},
                        "company": {"type": "string", "description": "公司名称"},
                        "position": {"type": "string", "description": "职位"},
                        "responsibilities": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "工作职责"
                        },
                        "technologies": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "使用的技术栈"
                        }
                    }
                },
                "description": "工作经验"
            },
            "projects": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "name": {"type": "string", "description": "项目名称"},
                        "period": {"type": "string", "description": "项目时间"},
                        "description": {"type": "string", "description": "项目描述"},
                        "achievements": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "项目成果"
                        }
                    }
                },
                "description": "项目经验"
            },
            "skills": {
                "type": "object",
                "properties": {
                    "programming_languages": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "编程语言"
                    },
                    "frameworks": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "框架技术"
                    },
                    "databases": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "数据库"
                    },
                    "tools": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "开发工具"
                    }
                },
                "description": "技能专长"
            },
            "certifications": {
                "type": "array",
                "items": {"type": "string"},
                "description": "证书资格"
            },
            "languages": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "language": {"type": "string", "description": "语言"},
                        "level": {"type": "string", "description": "水平"}
                    }
                },
                "description": "语言能力"
            },
            "awards": {
                "type": "array",
                "items": {"type": "string"},
                "description": "获奖荣誉"
            }
        },
        "required": ["personal_info", "education", "work_experience", "skills"]
    }
    
    # 少样本示例
    examples = [
        {
            "input": "李明，电话：139-8765-4321，邮箱：<EMAIL>",
            "output": {
                "personal_info": {
                    "name": "李明",
                    "phone": "139-8765-4321",
                    "email": "<EMAIL>"
                }
            }
        }
    ]
    
    print("👤 开始提取简历信息...")
    print(f"简历长度: {len(resume_text)} 字符")
    print("-" * 60)
    
    try:
        # 执行提取
        result = await extractor.extract(
            text_content=resume_text,
            schema=schema,
            few_shot_examples=examples,
            document_id="resume_zhangwei"
        )
        
        print(f"提取状态: {result.status}")
        print(f"处理时间: {result.processing_time:.2f} 秒")
        
        if result.is_success():
            print("\n✅ 简历信息提取成功！")
            
            data = result.extracted_data
            
            # 个人信息
            personal = data.get("personal_info", {})
            print(f"\n👤 个人信息:")
            print(f"  姓名: {personal.get('name')}")
            print(f"  电话: {personal.get('phone')}")
            print(f"  邮箱: {personal.get('email')}")
            print(f"  地址: {personal.get('address')}")
            print(f"  出生日期: {personal.get('birth_date')}")
            
            # 教育背景
            education = data.get("education", [])
            print(f"\n🎓 教育背景:")
            for edu in education:
                print(f"  {edu.get('period')} - {edu.get('school')}")
                print(f"    专业: {edu.get('major')} ({edu.get('degree')})")
                if edu.get('gpa'):
                    print(f"    GPA: {edu.get('gpa')}")
            
            # 工作经验
            work_exp = data.get("work_experience", [])
            print(f"\n💼 工作经验:")
            for work in work_exp:
                print(f"  {work.get('period')} - {work.get('company')}")
                print(f"    职位: {work.get('position')}")
                responsibilities = work.get("responsibilities", [])
                if responsibilities:
                    print(f"    职责:")
                    for resp in responsibilities[:3]:  # 只显示前3个
                        print(f"      • {resp}")
                technologies = work.get("technologies", [])
                if technologies:
                    print(f"    技术栈: {', '.join(technologies)}")
                print()
            
            # 项目经验
            projects = data.get("projects", [])
            print(f"\n🚀 项目经验:")
            for project in projects:
                print(f"  {project.get('name')} ({project.get('period')})")
                print(f"    描述: {project.get('description')}")
                achievements = project.get("achievements", [])
                if achievements:
                    print(f"    成果:")
                    for achievement in achievements:
                        print(f"      • {achievement}")
                print()
            
            # 技能专长
            skills = data.get("skills", {})
            print(f"\n🛠️ 技能专长:")
            if skills.get("programming_languages"):
                print(f"  编程语言: {', '.join(skills['programming_languages'])}")
            if skills.get("frameworks"):
                print(f"  框架技术: {', '.join(skills['frameworks'])}")
            if skills.get("databases"):
                print(f"  数据库: {', '.join(skills['databases'])}")
            if skills.get("tools"):
                print(f"  开发工具: {', '.join(skills['tools'])}")
            
            # 证书资格
            certifications = data.get("certifications", [])
            if certifications:
                print(f"\n🏆 证书资格:")
                for cert in certifications:
                    print(f"  • {cert}")
            
            # 语言能力
            languages = data.get("languages", [])
            if languages:
                print(f"\n🌍 语言能力:")
                for lang in languages:
                    print(f"  • {lang.get('language')}: {lang.get('level')}")
            
            # 获奖荣誉
            awards = data.get("awards", [])
            if awards:
                print(f"\n🏅 获奖荣誉:")
                for award in awards:
                    print(f"  • {award}")
        
        else:
            print("\n❌ 简历信息提取失败:")
            for error in result.errors:
                print(f"  - {error.error_type}: {error.error_message}")
    
    except Exception as e:
        print(f"❌ 程序异常: {str(e)}")


if __name__ == "__main__":
    # 检查API密钥
    if not os.getenv("LLM_API_KEY") or os.getenv("LLM_API_KEY") == "your-api-key-here":
        print("⚠️ 请设置环境变量 LLM_API_KEY")
        print("示例: export LLM_API_KEY='your-actual-api-key'")
        exit(1)
    
    asyncio.run(extract_resume())
