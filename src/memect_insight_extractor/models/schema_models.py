"""Schema相关模型"""

from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field


class SchemaField(BaseModel):
    """Schema字段模型

    Args:
        name: 字段名称
        type: 字段类型
        description: 字段描述
        required: 是否必需
        default: 默认值
        enum: 枚举值
        format: 格式约束
        pattern: 正则表达式模式
        minimum: 最小值
        maximum: 最大值
        min_length: 最小长度
        max_length: 最大长度
    """

    name: str = Field(description="字段名称")
    type: str = Field(description="字段类型")
    description: Optional[str] = Field(default=None, description="字段描述")
    required: bool = Field(default=False, description="是否必需")
    default: Optional[Any] = Field(default=None, description="默认值")
    enum: Optional[List[Any]] = Field(default=None, description="枚举值")
    format: Optional[str] = Field(default=None, description="格式约束")
    pattern: Optional[str] = Field(default=None, description="正则表达式模式")
    minimum: Optional[Union[int, float]] = Field(default=None, description="最小值")
    maximum: Optional[Union[int, float]] = Field(default=None, description="最大值")
    min_length: Optional[int] = Field(default=None, description="最小长度")
    max_length: Optional[int] = Field(default=None, description="最大长度")


class ExtractionSchema(BaseModel):
    """提取Schema模型

    Args:
        schema_id: Schema ID
        name: Schema名称
        description: Schema描述
        version: Schema版本
        fields: 字段列表
        json_schema: 完整的JSON Schema
    """

    schema_id: Optional[str] = Field(default=None, description="Schema ID")
    name: str = Field(description="Schema名称")
    description: Optional[str] = Field(default=None, description="Schema描述")
    version: str = Field(default="1.0.0", description="Schema版本")
    fields: List[SchemaField] = Field(description="字段列表")
    json_schema: Dict[str, Any] = Field(description="完整的JSON Schema")

    class Config:
        """Pydantic配置"""

        extra = "forbid"
        validate_assignment = True

    def to_json_schema(self) -> Dict[str, Any]:
        """转换为JSON Schema格式"""
        return self.json_schema

    def get_required_fields(self) -> List[str]:
        """获取必需字段列表"""
        return [field.name for field in self.fields if field.required]

    def get_field_by_name(self, name: str) -> Optional[SchemaField]:
        """根据名称获取字段"""
        for field in self.fields:
            if field.name == name:
                return field
        return None
