"""提取请求模型"""

from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class ExtractionRequest(BaseModel):
    """提取请求模型

    Args:
        text_content: 待提取的文档文本内容
        schema: JSON Schema定义
        config_version_id: 配置版本ID(可选)
        document_id: 文档ID(可选)
        extraction_purpose_id: 提取目的唯一标识(可选)
        system_prompt: 系统提示词(可选)
        user_prompt: 用户提示词(可选)
        prompt_template: 自定义提示词模板(可选，已废弃，建议使用system_prompt)
        few_shot_examples: 少样本示例(可选)
        custom_instructions: 自定义指令(可选)
    """

    text_content: str = Field(description="待提取的文档文本内容")
    schema: Dict[str, Any] = Field(description="JSON Schema定义")

    # 可选参数
    config_version_id: Optional[str] = Field(default=None, description="配置版本ID")
    document_id: Optional[str] = Field(default=None, description="文档ID")
    extraction_purpose_id: Optional[str] = Field(default=None, description="提取目的唯一标识")

    # 提示词配置
    system_prompt: Optional[str] = Field(default=None, description="系统提示词")
    user_prompt: Optional[str] = Field(default=None, description="用户提示词")
    prompt_template: Optional[str] = Field(default=None, description="自定义提示词模板(已废弃)")

    # 其他配置
    few_shot_examples: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="少样本示例"
    )
    custom_instructions: Optional[str] = Field(default=None, description="自定义指令")

    class Config:
        """Pydantic配置"""

        extra = "forbid"
        validate_assignment = True
