"""配置管理模块"""

from typing import Any, Dict, Optional

from pydantic import BaseModel, Field


class ExtractionConfig(BaseModel):
    """提取配置类

    Args:
        llm_base_url: 大模型API基础URL
        llm_api_key: API密钥
        llm_model: 模型名称
        llm_temperature: 温度参数
        llm_max_tokens: 最大token数
        llm_timeout: 请求超时时间(秒)
        max_retries: 最大重试次数
        retry_delay: 重试延迟(秒)
        max_chunk_tokens: 最大文本块token数
        chunk_overlap_tokens: 文本块重叠token数
        tokenizer_type: tokenizer类型（openai或qwen）
        enable_parallel: 是否启用并行处理
        max_parallel_chunks: 最大并行处理块数
    """

    # LLM配置
    llm_base_url: str = Field(
        default="https://api.openai.com/v1", description="LLM API基础URL"
    )
    llm_api_key: str = Field(description="LLM API密钥")
    llm_model: str = Field(default="gpt-4", description="LLM模型名称")
    llm_temperature: float = Field(
        default=0.1, ge=0.0, le=2.0, description="LLM温度参数"
    )
    llm_max_tokens: Optional[int] = Field(
        default=4000, gt=0, description="LLM最大token数"
    )
    llm_timeout: int = Field(default=60, gt=0, description="LLM请求超时时间")

    # 重试配置
    max_retries: int = Field(default=3, ge=0, description="最大重试次数")
    retry_delay: float = Field(default=1.0, ge=0, description="重试延迟时间")

    # 文本分段配置
    max_chunk_size: int = Field(default=3000, gt=0, description="最大文本块大小（字符数）")
    chunk_overlap: int = Field(default=200, ge=0, description="文本块重叠大小（字符数）")
    max_chunk_tokens: int = Field(default=2000, gt=0, description="最大文本块token数")
    chunk_overlap_tokens: int = Field(default=100, ge=0, description="文本块重叠token数")
    tokenizer_type: str = Field(default="openai", description="tokenizer类型：openai或qwen")

    # 并行处理配置
    enable_parallel: bool = Field(default=True, description="是否启用并行处理")
    max_parallel_chunks: int = Field(default=5, gt=0, description="最大并行处理块数")

    # 其他配置
    extra_headers: Dict[str, str] = Field(
        default_factory=dict, description="额外的HTTP头"
    )

    class Config:
        """Pydantic配置"""

        extra = "forbid"
        validate_assignment = True
