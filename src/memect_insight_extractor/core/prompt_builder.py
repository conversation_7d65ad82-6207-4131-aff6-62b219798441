"""动态提示词构建模块"""

import json
import re
from typing import Any, Dict, List, Optional

from ..models.extraction_request import ExtractionRequest
from ..utils.logger import get_logger

logger = get_logger(__name__)


class PromptBuilder:
    """动态提示词构建器

    根据配置和请求动态构建最优的提示词
    """

    DEFAULT_SYSTEM_INSTRUCTION = """你是一个世界级的信息提取专家。请根据下面提供的Schema，从文本中提取信息，并只返回一个符合该Schema的JSON对象。不要添加任何额外的解释或文字。

要求：
1. 严格按照Schema定义的结构和类型返回数据
2. 如果某个字段在文档中找不到对应信息，请设置为null
3. 确保返回的JSON格式正确，可以被解析
4. 不要包含任何注释或额外说明"""

    def __init__(self):
        """初始化提示词构建器"""
        pass

    def build_prompt(
        self, request: ExtractionRequest, text_chunk: Optional[str] = None
    ) -> str:
        """构建完整的提示词

        Args:
            request: 提取请求
            text_chunk: 文本块(如果为None则使用完整文本)

        Returns:
            构建的提示词
        """
        text_content = text_chunk if text_chunk is not None else request.text_content

        # 准备变量替换字典
        variables = self._prepare_variables(request, text_content)

        # 构建提示词各部分
        parts = []

        # 1. 系统指令
        system_instruction = self._build_system_instruction(request, variables)
        parts.append(system_instruction)

        # 2. Schema定义
        schema_section = self._build_schema_section(request.schema)
        parts.append(schema_section)

        # 3. 少样本示例(如果有)
        if request.few_shot_examples:
            examples_section = self._build_examples_section(request.few_shot_examples)
            parts.append(examples_section)

        # 4. 自定义指令(如果有)
        if request.custom_instructions:
            custom_instructions = self._replace_variables(request.custom_instructions, variables)
            parts.append(f"\n附加指令：\n{custom_instructions}")

        # 5. 用户提示词(如果有)
        if request.user_prompt:
            user_prompt = self._replace_variables(request.user_prompt, variables)
            parts.append(f"\n用户指令：\n{user_prompt}")

        # 6. 源文档文本
        text_section = self._build_text_section(text_content)
        parts.append(text_section)

        # 7. 输出格式要求
        output_instruction = self._build_output_instruction()
        parts.append(output_instruction)

        prompt = "\n\n".join(parts)

        logger.debug(f"构建的提示词长度: {len(prompt)} 字符")
        logger.debug(f"提示词内容:\n{prompt}")

        return prompt

    def _build_system_instruction(self, request: ExtractionRequest, variables: Dict[str, str]) -> str:
        """构建系统指令部分

        Args:
            request: 提取请求
            variables: 变量替换字典

        Returns:
            系统指令文本
        """
        # 优先使用新的system_prompt
        if request.system_prompt:
            return self._replace_variables(request.system_prompt, variables)

        # 兼容旧的prompt_template
        if request.prompt_template:
            return self._replace_variables(request.prompt_template, variables)

        return self.DEFAULT_SYSTEM_INSTRUCTION

    def _build_schema_section(self, schema: Dict[str, Any]) -> str:
        """构建Schema定义部分

        Args:
            schema: JSON Schema

        Returns:
            Schema定义文本
        """
        schema_json = json.dumps(schema, ensure_ascii=False, indent=2)
        return f"JSON Schema定义：\n```json\n{schema_json}\n```"

    def _build_examples_section(self, examples: List[Dict[str, Any]]) -> str:
        """构建少样本示例部分

        Args:
            examples: 示例列表

        Returns:
            示例文本
        """
        examples_text = "参考示例：\n"

        for i, example in enumerate(examples, 1):
            if "input" in example and "output" in example:
                input_text = example["input"]
                output_json = json.dumps(
                    example["output"], ensure_ascii=False, indent=2
                )
                examples_text += f"\n示例 {i}：\n"
                examples_text += f"输入文本：{input_text}\n"
                examples_text += f"输出JSON：\n```json\n{output_json}\n```\n"

        return examples_text

    def _build_text_section(self, text_content: str) -> str:
        """构建文档文本部分

        Args:
            text_content: 文档文本

        Returns:
            文档文本部分
        """
        return f"待提取的文档内容：\n{text_content}"

    def _build_output_instruction(self) -> str:
        """构建输出格式指令

        Returns:
            输出格式指令
        """
        return "请根据以上Schema和文档内容，返回提取的JSON数据："

    def estimate_token_count(self, text: str) -> int:
        """估算文本的token数量

        Args:
            text: 文本内容

        Returns:
            估算的token数量
        """
        # 简单估算：中文按字符数，英文按单词数的1.3倍
        chinese_chars = sum(1 for char in text if "\u4e00" <= char <= "\u9fff")
        other_chars = len(text) - chinese_chars
        english_words = len(text.split())

        # 中文字符按1:1计算，英文按1.3倍计算
        estimated_tokens = chinese_chars + int(english_words * 1.3)

        return estimated_tokens

    def _prepare_variables(self, request: ExtractionRequest, text_content: str) -> Dict[str, str]:
        """准备变量替换字典

        Args:
            request: 提取请求
            text_content: 文本内容

        Returns:
            变量替换字典
        """
        variables = {
            "text_content": text_content,
            "document_id": request.document_id or "",
            "config_version_id": request.config_version_id or "",
            "extraction_purpose_id": request.extraction_purpose_id or "",
            "schema_json": json.dumps(request.schema, ensure_ascii=False, indent=2),
        }

        # 添加schema相关变量
        if "properties" in request.schema:
            field_names = list(request.schema["properties"].keys())
            variables["field_names"] = ", ".join(field_names)
            variables["field_count"] = str(len(field_names))

        return variables

    def _replace_variables(self, text: str, variables: Dict[str, str]) -> str:
        """替换文本中的变量

        Args:
            text: 原始文本
            variables: 变量字典

        Returns:
            替换后的文本
        """
        if not text:
            return text

        # 支持 ${variable_name} 和 {variable_name} 两种格式
        def replace_func(match):
            var_name = match.group(1)
            return variables.get(var_name, match.group(0))

        # 替换 ${variable_name} 格式
        text = re.sub(r'\$\{([^}]+)\}', replace_func, text)

        # 替换 {variable_name} 格式
        text = re.sub(r'\{([^}]+)\}', replace_func, text)

        return text
