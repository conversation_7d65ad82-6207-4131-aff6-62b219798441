"""动态提示词构建模块"""

import json
import re
from typing import Any, Dict, List, Optional

from ..models.extraction_request import ExtractionRequest
from ..utils.logger import get_logger

logger = get_logger(__name__)


class PromptBuilder:
    """动态提示词构建器

    根据配置和请求动态构建最优的提示词
    """

    DEFAULT_SYSTEM_INSTRUCTION = """你是一个世界级的信息提取专家。请根据下面提供的Schema，从文本中提取信息，并只返回一个符合该Schema的JSON对象。不要添加任何额外的解释或文字。

要求：
1. 严格按照Schema定义的结构和类型返回数据
2. 如果某个字段在文档中找不到对应信息，请设置为null
3. 确保返回的JSON格式正确，可以被解析
4. 不要包含任何注释或额外说明"""

    def __init__(self):
        """初始化提示词构建器"""
        pass

    def build_prompt(
        self, request: ExtractionRequest, text_chunk: Optional[str] = None
    ) -> str:
        """构建完整的提示词

        Args:
            request: 提取请求
            text_chunk: 文本块(如果为None则使用完整文本)

        Returns:
            构建的提示词
        """
        text_content = text_chunk if text_chunk is not None else request.text_content

        # 构建提示词各部分
        parts = []

        # 1. 系统指令
        system_instruction = self._build_system_instruction(request)
        parts.append(system_instruction)

        # 2. Schema定义
        schema_section = self._build_schema_section(request.schema)
        parts.append(schema_section)

        # 3. 少样本示例(如果有)
        if request.few_shot_examples:
            examples_section = self._build_examples_section(request.few_shot_examples)
            parts.append(examples_section)

        # 4. 自定义指令(如果有)
        if request.custom_instructions:
            parts.append(f"\n附加指令：\n{request.custom_instructions}")

        # 5. 源文档文本
        text_section = self._build_text_section(text_content)
        parts.append(text_section)

        # 6. 输出格式要求
        output_instruction = self._build_output_instruction()
        parts.append(output_instruction)

        prompt = "\n\n".join(parts)

        logger.debug(f"构建的提示词长度: {len(prompt)} 字符")
        logger.debug(f"提示词内容:\n{prompt}")

        return prompt

    def _build_system_instruction(self, request: ExtractionRequest) -> str:
        """构建系统指令部分

        Args:
            request: 提取请求

        Returns:
            系统指令文本
        """
        if request.prompt_template:
            return request.prompt_template
        return self.DEFAULT_SYSTEM_INSTRUCTION

    def _build_schema_section(self, schema: Dict[str, Any]) -> str:
        """构建Schema定义部分

        Args:
            schema: JSON Schema

        Returns:
            Schema定义文本
        """
        schema_json = json.dumps(schema, ensure_ascii=False, indent=2)
        return f"JSON Schema定义：\n```json\n{schema_json}\n```"

    def _build_examples_section(self, examples: List[Dict[str, Any]]) -> str:
        """构建少样本示例部分

        Args:
            examples: 示例列表

        Returns:
            示例文本
        """
        examples_text = "参考示例：\n"

        for i, example in enumerate(examples, 1):
            if "input" in example and "output" in example:
                input_text = example["input"]
                output_json = json.dumps(
                    example["output"], ensure_ascii=False, indent=2
                )
                examples_text += f"\n示例 {i}：\n"
                examples_text += f"输入文本：{input_text}\n"
                examples_text += f"输出JSON：\n```json\n{output_json}\n```\n"

        return examples_text

    def _build_text_section(self, text_content: str) -> str:
        """构建文档文本部分

        Args:
            text_content: 文档文本

        Returns:
            文档文本部分
        """
        return f"待提取的文档内容：\n{text_content}"

    def _build_output_instruction(self) -> str:
        """构建输出格式指令

        Returns:
            输出格式指令
        """
        return "请根据以上Schema和文档内容，返回提取的JSON数据："

    def estimate_token_count(self, text: str) -> int:
        """估算文本的token数量

        Args:
            text: 文本内容

        Returns:
            估算的token数量
        """
        # 简单估算：中文按字符数，英文按单词数的1.3倍
        chinese_chars = sum(1 for char in text if "\u4e00" <= char <= "\u9fff")
        other_chars = len(text) - chinese_chars
        english_words = len(text.split())

        # 中文字符按1:1计算，英文按1.3倍计算
        estimated_tokens = chinese_chars + int(english_words * 1.3)

        return estimated_tokens
