# Memect Insight Extractor

大模型文档信息提取模块，用于从文档中智能提取结构化信息。

## 功能特性

- **动态提示词工程**: 根据文档内容和配置版本动态构建最优提示词
- **智能文本分割**: 基于token数的智能分割，支持Markdown格式，保持章节结构完整性
- **完全离线运行**: 所有tokenizer文件随库安装，支持OpenAI和Qwen tokenizer，无需联网
- **分段并发处理**: 智能分割超长文档，支持并发处理提升效率
- **多模型支持**: 兼容 OpenAI API 格式的各种大语言模型
- **结果校验**: 基于 JSON Schema 的严格格式和类型校验
- **错误处理**: 完善的错误处理和自动重试机制
- **高度可配置**: 支持灵活的配置管理

## 安装

```bash
# 使用 uv 安装
uv pip install memect-insight-extractor

# 或从源码安装
git clone <repository-url>
cd memect_insight_extractor
make setup
```

## 快速开始

```python
from memect_insight_extractor import DocumentExtractor
from memect_insight_extractor.config import ExtractionConfig

# 配置提取器
config = ExtractionConfig(
    llm_base_url="https://api.openai.com/v1",
    llm_api_key="your-api-key",
    llm_model="gpt-4",
    max_chunk_tokens=2000,      # 基于token数分割
    tokenizer_type="openai"     # 使用OpenAI tokenizer
)

extractor = DocumentExtractor(config)

# 执行提取
result = await extractor.extract(
    text_content="待提取的文档内容...",
    schema={
        "type": "object",
        "properties": {
            "company_name": {"type": "string", "description": "公司名称"},
            "amount": {"type": "number", "description": "金额"}
        }
    }
)

print(result.extracted_data)
```

## 开发

```bash
# 设置开发环境
make setup

# 运行测试
make test

# 代码格式化
make format

# 代码检查
make lint

# 构建包
make build
```

## 文档

详细文档请参考 `docs/` 目录或运行：

```bash
make docs-serve
```

## 许可证

[MIT License](LICENSE)
