# 提取器 API

## DocumentExtractor

文档提取器是模块的核心类，负责协调整个信息提取流程。

### 初始化

```python
from memect_insight_extractor import DocumentExtractor, ExtractionConfig

config = ExtractionConfig(llm_api_key="your-api-key")
extractor = DocumentExtractor(config)
```

### 方法

#### extract

执行信息提取的主要方法。

```python
async def extract(
    self,
    text_content: str,
    schema: Dict[str, Any],
    **kwargs
) -> ExtractionResult
```

**参数:**

- `text_content` (`str`): 待提取的文档文本内容
- `schema` (`Dict[str, Any]`): JSON Schema 定义
- `**kwargs`: 其他可选参数，会传递给 `ExtractionRequest`

**返回:**

- `ExtractionResult`: 提取结果对象

**示例:**

```python
schema = {
    "type": "object",
    "properties": {
        "company_name": {"type": "string", "description": "公司名称"},
        "amount": {"type": "number", "description": "金额"}
    },
    "required": ["company_name"]
}

result = await extractor.extract(
    text_content="合同甲方：ABC公司，金额：10万元",
    schema=schema
)

if result.is_success():
    print(result.extracted_data)
```

#### extract_from_request

从请求对象执行提取。

```python
async def extract_from_request(
    self, 
    request: ExtractionRequest
) -> ExtractionResult
```

**参数:**

- `request` (`ExtractionRequest`): 提取请求对象

**返回:**

- `ExtractionResult`: 提取结果对象

**示例:**

```python
from memect_insight_extractor import ExtractionRequest

request = ExtractionRequest(
    text_content="文档内容...",
    schema=schema,
    prompt_template="自定义提示词模板",
    few_shot_examples=[
        {
            "input": "示例输入",
            "output": {"field": "value"}
        }
    ]
)

result = await extractor.extract_from_request(request)
```

### 高级功能

#### 自定义提示词

```python
custom_prompt = """
你是一个专业的合同信息提取专家。
请仔细阅读合同内容，提取关键信息。
注意：金额请转换为数字格式。
"""

result = await extractor.extract(
    text_content=text_content,
    schema=schema,
    prompt_template=custom_prompt
)
```

#### 少样本学习

```python
examples = [
    {
        "input": "甲方：ABC公司，金额：5万元",
        "output": {
            "company_name": "ABC公司",
            "amount": 50000
        }
    }
]

result = await extractor.extract(
    text_content=text_content,
    schema=schema,
    few_shot_examples=examples
)
```

#### 自定义指令

```python
result = await extractor.extract(
    text_content=text_content,
    schema=schema,
    custom_instructions="请特别注意日期格式的标准化"
)
```

### 处理流程

1. **接收请求**: 创建 `ExtractionRequest` 对象
2. **文本分段**: 如果文本过长，自动分段处理
3. **构建提示词**: 动态组合系统指令、Schema、示例等
4. **调用 LLM**: 与大语言模型 API 通信
5. **解析结果**: 解析 JSON 并进行 Schema 校验
6. **错误处理**: 处理各种异常情况，支持重试和自我修正
7. **结果合并**: 将多个文本块的结果合并

### 错误处理

提取器具有完善的错误处理机制：

- **网络错误**: 自动重试
- **JSON 解析错误**: 尝试修复和重新解析
- **Schema 校验错误**: 记录详细错误信息
- **超时错误**: 记录并继续处理其他块

### 性能优化

- **并行处理**: 支持多个文本块并行处理
- **智能分段**: 按章节和语义分割文本
- **缓存机制**: 避免重复计算
- **资源管理**: 自动管理 HTTP 连接
