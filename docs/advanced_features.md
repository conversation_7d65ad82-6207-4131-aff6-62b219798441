# 高级功能说明

本文档介绍了模块的高级功能特性。

## 1. 系统提示词和用户提示词支持

### 功能概述

支持分别设置系统提示词（`system_prompt`）和用户提示词（`user_prompt`），提供更灵活的提示词配置方式。

### 主要特性

- **分离关注点**: 系统提示词定义角色和基本规则，用户提示词定义具体任务
- **变量替换**: 支持在提示词中使用变量，动态插入内容
- **向后兼容**: 保持对旧版 `prompt_template` 的兼容性

### 使用示例

```python
from memect_insight_extractor import ExtractionRequest

request = ExtractionRequest(
    text_content="合同内容...",
    schema=schema,
    system_prompt="""你是一个专业的合同分析专家。
你具有以下能力：
- 准确识别合同关键信息
- 理解法律条款含义
- 提取结构化数据

当前任务：分析文档 {document_id}，提取目的：{extraction_purpose_id}
""",
    user_prompt="""请从合同文档中提取以下信息：
- 需要提取的字段：{field_names}
- 字段总数：{field_count}

请确保数据准确性和完整性。
"""
)
```

### 支持的变量

| 变量名 | 描述 | 示例值 |
|--------|------|--------|
| `{text_content}` | 文档内容 | "合同内容..." |
| `{document_id}` | 文档ID | "DOC_001" |
| `{config_version_id}` | 配置版本ID | "v2.1.0" |
| `{extraction_purpose_id}` | 提取目的ID | "contract_analysis" |
| `{schema_json}` | Schema的JSON字符串 | `{"type": "object", ...}` |
| `{field_names}` | 字段名称列表 | "name, amount, date" |
| `{field_count}` | 字段数量 | "3" |

### 变量格式

支持两种变量格式：
- `{variable_name}` - 标准格式
- `${variable_name}` - Shell风格格式

## 2. Schema主键支持

### 功能概述

为Schema字段添加主键标识，便于数据合并和去重操作。

### 使用示例

```python
from memect_insight_extractor.models.schema_models import SchemaField, ExtractionSchema

# 定义带主键的字段
fields = [
    SchemaField(
        name="contract_id",
        type="string",
        description="合同编号",
        required=True,
        is_primary_key=True  # 标记为主键
    ),
    SchemaField(
        name="company_name",
        type="string",
        description="公司名称",
        required=True
    ),
    SchemaField(
        name="amount",
        type="number",
        description="合同金额"
    )
]

# 创建Schema
schema = ExtractionSchema(
    name="contract_schema",
    fields=fields,
    json_schema={"type": "object", "properties": {...}}
)

# 获取主键字段
primary_keys = schema.get_primary_key_fields()  # ["contract_id"]
required_fields = schema.get_required_fields()  # ["contract_id", "company_name"]
```

### 应用场景

- **数据合并**: 使用主键字段合并多个提取结果
- **去重处理**: 基于主键识别重复数据
- **数据验证**: 确保主键字段的唯一性

## 3. 提取目的唯一标识

### 功能概述

为每个提取任务添加唯一标识，便于追踪和管理不同的提取场景。

### 使用示例

```python
request = ExtractionRequest(
    text_content="发票内容...",
    schema=invoice_schema,
    extraction_purpose_id="invoice_processing_v2.1",  # 提取目的标识
    document_id="INV_20240115_001"
)
```

### 命名建议

- **格式**: `{业务类型}_{版本号}`
- **示例**:
  - `contract_analysis_v1.0` - 合同分析v1.0
  - `invoice_processing_v2.1` - 发票处理v2.1
  - `resume_extraction_v1.5` - 简历提取v1.5

### 应用场景

- **任务追踪**: 跟踪不同类型的提取任务
- **版本管理**: 管理提取逻辑的不同版本
- **性能分析**: 分析不同提取目的的性能表现
- **错误诊断**: 快速定位特定场景的问题

## 4. DEBUG模式

### 功能概述

提供详细的调试信息，包括大模型输入输出、处理过程等。

### 配置方式

```python
from memect_insight_extractor import ExtractionConfig, DocumentExtractor

config = ExtractionConfig(
    llm_api_key="your-api-key",
    debug_mode=True,           # 启用DEBUG模式
    log_level="DEBUG",         # 设置日志级别
    log_file="debug.log"       # 输出到日志文件
)

extractor = DocumentExtractor(config)
```

### DEBUG信息内容

#### 1. 配置信息
- 提取器初始化配置
- 模型参数设置
- 处理策略选择

#### 2. 请求详情
- 文档长度和特征
- Schema字段分析
- 提示词构建过程

#### 3. LLM交互
- **输入**: 完整的提示词内容
- **输出**: LLM的原始响应
- **Token使用**: 输入/输出token统计
- **API调用**: 请求头、响应状态等

#### 4. 处理过程
- 文本分段策略
- 并行处理状态
- 结果解析过程
- 错误处理详情

#### 5. 结果分析
- 提取数据字段
- 校验错误详情
- 性能指标统计

### 日志示例

```
2024-01-15 10:30:15.123 | DEBUG    | extractor:__init__:45 | DocumentExtractor初始化完成，配置: {...}
2024-01-15 10:30:15.124 | DEBUG    | extractor:extract_from_request:95 | 提取请求详情: document_id=DOC_001, extraction_purpose_id=contract_analysis_v1
2024-01-15 10:30:15.125 | DEBUG    | llm_client:extract_text:145 | 输入提示词长度: 1250 字符
2024-01-15 10:30:15.126 | DEBUG    | llm_client:extract_text:146 | 输入提示词内容: 你是一个专业的合同分析专家...
2024-01-15 10:30:16.890 | DEBUG    | llm_client:chat_completion:115 | 完整LLM API响应: {"choices": [...], "usage": {...}}
2024-01-15 10:30:16.891 | DEBUG    | llm_client:extract_text:158 | LLM输出内容: {"contract_id": "CT-001", ...}
```

## 5. 向后兼容性

### 兼容性保证

所有功能都保持向后兼容：

1. **旧版提示词**: `prompt_template` 仍然支持，但建议迁移到 `system_prompt`
2. **现有API**: 所有现有的API接口保持不变
3. **配置参数**: 新增的配置参数都有合理的默认值

### 迁移建议

#### 从 prompt_template 迁移到 system_prompt

```python
# 旧版本
request = ExtractionRequest(
    text_content="文档内容",
    schema=schema,
    prompt_template="你是专业的分析专家，请提取关键信息。"
)

# 新版本（推荐）
request = ExtractionRequest(
    text_content="文档内容",
    schema=schema,
    system_prompt="你是专业的分析专家。",
    user_prompt="请提取 {field_names} 信息。"
)
```

#### 启用高级功能

```python
# 完整的高级功能使用示例
config = ExtractionConfig(
    llm_api_key="your-api-key",
    debug_mode=True,
    log_level="DEBUG"
)

request = ExtractionRequest(
    text_content="文档内容",
    schema=schema_with_primary_keys,
    extraction_purpose_id="business_analysis_v1.0",
    system_prompt="你是专业的 {extraction_purpose_id} 专家。",
    user_prompt="请提取 {field_names} 信息。"
)

extractor = DocumentExtractor(config)
result = await extractor.extract_from_request(request)
```
