# Memect Insight Extractor

欢迎使用 Memect Insight Extractor，这是一个强大的大模型文档信息提取模块。

## 功能特性

### 🚀 动态提示词工程
根据文档内容和配置版本动态构建最优的提示词，提高提取准确性。

### 📄 分段并发处理
智能分割超长文档，支持并发处理，大幅提升处理效率。

### 🔌 多模型支持
兼容 OpenAI API 格式的各种大语言模型，包括 GPT、Claude、Gemini 等。

### ✅ 结果校验
基于 JSON Schema 的严格格式和类型校验，确保提取结果的准确性。

### 🔄 错误处理
完善的错误处理和自动重试机制，包括自我修正功能。

### ⚙️ 高度可配置
支持灵活的配置管理，满足不同场景的需求。

## 核心架构

```mermaid
graph TD
    A[文档输入] --> B[文本分段器]
    B --> C[提示词构建器]
    C --> D[LLM客户端]
    D --> E[结果解析器]
    E --> F[错误处理器]
    F --> G[提取结果]
    
    H[配置管理] --> C
    H --> D
    H --> F
```

## 处理流程

1. **接收请求**: 接收文档内容和提取Schema
2. **文本分段**: 根据文档长度和复杂度进行智能分段
3. **构建提示词**: 动态组合系统指令、Schema定义、示例等
4. **调用LLM**: 与大语言模型API进行通信
5. **解析校验**: 解析JSON结果并进行Schema校验
6. **错误处理**: 处理各种异常情况，支持自动重试和修正
7. **结果合并**: 将多个文本块的结果合并为最终结果

## 快速开始

```python
from memect_insight_extractor import DocumentExtractor, ExtractionConfig

# 配置提取器
config = ExtractionConfig(
    llm_base_url="https://api.openai.com/v1",
    llm_api_key="your-api-key",
    llm_model="gpt-4"
)

extractor = DocumentExtractor(config)

# 定义提取Schema
schema = {
    "type": "object",
    "properties": {
        "company_name": {"type": "string", "description": "公司名称"},
        "amount": {"type": "number", "description": "金额"},
        "date": {"type": "string", "description": "日期"}
    },
    "required": ["company_name"]
}

# 执行提取
result = await extractor.extract(
    text_content="您的文档内容...",
    schema=schema
)

if result.is_success():
    print("提取成功:", result.extracted_data)
else:
    print("提取失败:", result.errors)
```

## 下一步

- [快速开始](quickstart.md) - 详细的安装和使用指南
- [API参考](api/config.md) - 完整的API文档
- [示例](examples.md) - 更多使用示例
- [常见问题](faq.md) - 常见问题解答
