.PHONY: install dev-install test lint format clean build publish docs download-tokenizers

# 安装依赖
install:
	uv pip install -e .

# 安装开发依赖
dev-install:
	uv pip install -e ".[dev,docs]"

# 运行测试
test:
	pytest

# 代码检查
lint:
	flake8 src tests
	mypy src

# 代码格式化
format:
	black src tests
	isort src tests

# 清理构建文件
clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf htmlcov/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

# 构建包
build: clean download-tokenizers
	uv build

# 发布包
publish: build
	uv publish

# 生成文档
docs:
	mkdocs build

# 启动文档服务器
docs-serve:
	mkdocs serve

# 下载tokenizer文件
download-tokenizers:
	python scripts/download_tokenizers.py

# 完整的开发环境设置
setup: dev-install download-tokenizers
	@echo "开发环境设置完成"

# 运行完整的CI检查
ci: format lint test
	@echo "CI检查完成"
