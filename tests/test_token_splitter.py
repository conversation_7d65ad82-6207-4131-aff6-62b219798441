"""测试基于token的文本分割功能"""

import pytest

from memect_insight_extractor.config import ExtractionConfig
from memect_insight_extractor.core.text_splitter import TextSplitter


class TestTokenBasedSplitter:
    """测试基于token的文本分割器"""

    @pytest.fixture
    def openai_config(self):
        """OpenAI tokenizer配置"""
        return ExtractionConfig(
            llm_api_key="test-key",
            llm_model="gpt-4",
            max_chunk_tokens=100,
            chunk_overlap_tokens=20,
            tokenizer_type="openai",
            max_chunk_size=1000,
            enable_parallel=False,
        )

    @pytest.fixture
    def qwen_config(self):
        """Qwen tokenizer配置"""
        return ExtractionConfig(
            llm_api_key="test-key",
            llm_model="qwen",
            max_chunk_tokens=100,
            chunk_overlap_tokens=20,
            tokenizer_type="qwen",
            max_chunk_size=1000,
            enable_parallel=False,
        )

    @pytest.fixture
    def markdown_text(self):
        """Markdown测试文本"""
        return """# 第一章 介绍

这是第一章的内容，包含了一些基本的介绍信息。

## 1.1 背景

这里是背景信息的详细描述。

```python
def hello_world():
    print("Hello, World!")
```

## 1.2 目标

这里描述了项目的目标和期望。

# 第二章 实现

这是第二章的内容。

## 2.1 架构设计

系统架构的详细说明。

| 组件 | 功能 | 状态 |
|------|------|------|
| 前端 | 用户界面 | 完成 |
| 后端 | 业务逻辑 | 进行中 |

## 2.2 技术选型

技术栈的选择和理由。

> 这是一个重要的引用块
> 包含了关键的设计决策

# 第三章 总结

项目的总结和未来规划。
"""

    def test_openai_tokenizer_initialization(self, openai_config):
        """测试OpenAI tokenizer初始化"""
        splitter = TextSplitter(openai_config)
        
        # 测试token计数
        text = "Hello, world! 你好世界！"
        token_count = splitter.count_tokens(text)
        assert token_count > 0
        assert isinstance(token_count, int)

    def test_qwen_tokenizer_fallback(self, qwen_config):
        """测试Qwen tokenizer回退机制"""
        splitter = TextSplitter(qwen_config)
        
        # 即使Qwen tokenizer可能不可用，也应该能够回退到字符估算
        text = "Hello, world! 你好世界！"
        token_count = splitter.count_tokens(text)
        assert token_count > 0
        assert isinstance(token_count, int)

    def test_token_based_splitting(self, openai_config):
        """测试基于token的分割"""
        splitter = TextSplitter(openai_config)
        
        # 创建一个足够长的文本来触发分割
        long_text = "这是一个很长的文本。" * 50
        
        chunks = splitter.split_text(long_text)
        
        # 验证分割结果
        assert len(chunks) > 1
        
        # 验证每个块都有token计数
        for chunk in chunks:
            assert chunk.token_count is not None
            assert chunk.token_count > 0
            
        # 验证token数不超过限制
        for chunk in chunks[:-1]:  # 最后一个块可能较小
            assert chunk.token_count <= openai_config.max_chunk_tokens

    def test_markdown_section_splitting(self, openai_config, markdown_text):
        """测试Markdown章节分割"""
        splitter = TextSplitter(openai_config)
        
        chunks = splitter.split_text(markdown_text)
        
        # 验证分割结果
        assert len(chunks) > 0
        
        # 验证每个块都有token计数
        for chunk in chunks:
            assert chunk.token_count is not None
            
        # 验证章节完整性（第一个块应该包含第一章标题）
        first_chunk_content = chunks[0].content
        assert "# 第一章 介绍" in first_chunk_content

    def test_code_block_preservation(self, openai_config):
        """测试代码块完整性保护"""
        text_with_code = """# 代码示例

这里有一个代码块：

```python
def complex_function():
    for i in range(100):
        print(f"Processing {i}")
        if i % 10 == 0:
            print("Checkpoint reached")
    return "Done"
```

代码块结束。
"""
        
        splitter = TextSplitter(openai_config)
        chunks = splitter.split_text(text_with_code)
        
        # 验证代码块没有被分割
        code_found = False
        for chunk in chunks:
            if "```python" in chunk.content:
                assert "```" in chunk.content.split("```python")[1]
                code_found = True
                break
        
        assert code_found, "代码块应该被完整保留"

    def test_table_preservation(self, openai_config):
        """测试表格完整性保护"""
        text_with_table = """# 数据表格

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

表格结束。
"""
        
        splitter = TextSplitter(openai_config)
        chunks = splitter.split_text(text_with_table)
        
        # 验证表格没有被分割
        table_found = False
        for chunk in chunks:
            content = chunk.content
            if "|" in content and "列1" in content:
                # 表格应该完整
                lines = content.split("\n")
                table_lines = [line for line in lines if "|" in line]
                assert len(table_lines) >= 3  # 标题行 + 分隔行 + 至少一行数据
                table_found = True
                break
        
        assert table_found, "表格应该被完整保留"

    def test_should_split_token_based(self, openai_config):
        """测试基于token的分割判断"""
        splitter = TextSplitter(openai_config)
        
        # 短文本不应该分割
        short_text = "这是一个短文本。"
        assert not splitter.should_split(short_text)
        
        # 长文本应该分割
        long_text = "这是一个很长的文本。" * 100
        assert splitter.should_split(long_text)

    def test_token_count_accuracy(self, openai_config):
        """测试token计数准确性"""
        splitter = TextSplitter(openai_config)
        
        # 测试中英文混合文本
        mixed_text = "Hello world! 你好世界！This is a test. 这是一个测试。"
        token_count = splitter.count_tokens(mixed_text)
        
        # token数应该合理（不会太小或太大）
        char_count = len(mixed_text)
        assert token_count > char_count // 10  # 不会太小
        assert token_count < char_count * 2    # 不会太大

    def test_chunk_overlap_tokens(self, openai_config):
        """测试token重叠功能"""
        # 设置较小的chunk大小来确保分割
        config = ExtractionConfig(
            llm_api_key="test-key",
            max_chunk_tokens=50,
            chunk_overlap_tokens=10,
            tokenizer_type="openai",
        )
        
        splitter = TextSplitter(config)
        long_text = "这是一个测试句子。" * 20
        
        chunks = splitter.split_text(long_text)
        
        if len(chunks) > 1:
            # 验证相邻块之间有重叠
            for i in range(len(chunks) - 1):
                current_chunk = chunks[i].content
                next_chunk = chunks[i + 1].content
                
                # 应该有一些重叠内容
                current_words = current_chunk.split()
                next_words = next_chunk.split()
                
                # 检查是否有重叠的词汇
                overlap_found = any(word in next_words for word in current_words[-5:])
                assert overlap_found, f"块 {i} 和 {i+1} 之间应该有重叠"
