"""测试校验工具"""

import pytest

from memect_insight_extractor.utils.validators import DataValidator, SchemaValidator


class TestDataValidator:
    """测试数据校验器"""

    def test_validate_email(self):
        """测试邮箱校验"""
        # 有效邮箱
        assert DataValidator.validate_email("<EMAIL>")
        assert DataValidator.validate_email("<EMAIL>")

        # 无效邮箱
        assert not DataValidator.validate_email("invalid-email")
        assert not DataValidator.validate_email("@domain.com")
        assert not DataValidator.validate_email("user@")
        assert not DataValidator.validate_email("")
        assert not DataValidator.validate_email(None)

    def test_validate_phone(self):
        """测试电话校验"""
        # 有效电话
        assert DataValidator.validate_phone("13812345678")
        assert DataValidator.validate_phone("138-1234-5678")
        assert DataValidator.validate_phone("010-12345678")
        assert DataValidator.validate_phone("0571-87654321")

        # 无效电话
        assert not DataValidator.validate_phone("123")
        assert not DataValidator.validate_phone("abc123")
        assert not DataValidator.validate_phone("")
        assert not DataValidator.validate_phone(None)

    def test_validate_date(self):
        """测试日期校验"""
        # 有效日期
        assert DataValidator.validate_date("2024-01-15")
        assert DataValidator.validate_date("2024/01/15")
        assert DataValidator.validate_date("2024年01月15日")
        assert DataValidator.validate_date("2024-01-15 10:30:00")

        # 无效日期
        assert not DataValidator.validate_date("2024-13-01")  # 无效月份
        assert not DataValidator.validate_date("invalid-date")
        assert not DataValidator.validate_date("")
        assert not DataValidator.validate_date(None)

    def test_validate_number(self):
        """测试数字校验"""
        # 有效数字
        assert DataValidator.validate_number("123.45")
        assert DataValidator.validate_number(123.45)
        assert DataValidator.validate_number("1,234.56")
        assert DataValidator.validate_number(100, min_value=0, max_value=200)

        # 无效数字
        assert not DataValidator.validate_number("abc")
        assert not DataValidator.validate_number("")
        assert not DataValidator.validate_number(None)
        assert not DataValidator.validate_number(50, min_value=100)  # 小于最小值
        assert not DataValidator.validate_number(300, max_value=200)  # 大于最大值

    def test_validate_url(self):
        """测试URL校验"""
        # 有效URL
        assert DataValidator.validate_url("https://www.example.com")
        assert DataValidator.validate_url("http://example.com/path?param=value")
        assert DataValidator.validate_url("https://api.example.com:8080/v1/data")

        # 无效URL
        assert not DataValidator.validate_url("not-a-url")
        assert not DataValidator.validate_url("ftp://example.com")  # 不支持ftp
        assert not DataValidator.validate_url("")
        assert not DataValidator.validate_url(None)

    def test_validate_id_card(self):
        """测试身份证校验"""
        # 有效身份证(示例，非真实)
        assert DataValidator.validate_id_card("11010119900101001X")
        assert DataValidator.validate_id_card("110101199001010010")

        # 无效身份证
        assert not DataValidator.validate_id_card("123456789")  # 长度不对
        assert not DataValidator.validate_id_card("11010119900101001Y")  # 校验码错误
        assert not DataValidator.validate_id_card("")
        assert not DataValidator.validate_id_card(None)

    def test_validate_amount(self):
        """测试金额校验"""
        # 有效金额
        assert DataValidator.validate_amount("123.45")
        assert DataValidator.validate_amount("1,234.56")
        assert DataValidator.validate_amount("¥1000.00")
        assert DataValidator.validate_amount("$500")

        # 无效金额
        assert not DataValidator.validate_amount("123.456")  # 小数位过多
        assert not DataValidator.validate_amount("abc")
        assert not DataValidator.validate_amount("")
        assert not DataValidator.validate_amount(None)


class TestSchemaValidator:
    """测试Schema校验器"""

    def test_validate_json_schema_valid(self):
        """测试有效Schema"""
        schema = {
            "type": "object",
            "properties": {"name": {"type": "string"}, "age": {"type": "integer"}},
            "required": ["name"],
        }

        errors = SchemaValidator.validate_json_schema(schema)
        assert len(errors) == 0

    def test_validate_json_schema_invalid(self):
        """测试无效Schema"""
        # 缺少type字段
        schema = {"properties": {"name": {"type": "string"}}}

        errors = SchemaValidator.validate_json_schema(schema)
        assert len(errors) > 0
        assert any("缺少type字段" in error for error in errors)

    def test_validate_data_against_schema_valid(self):
        """测试数据符合Schema"""
        schema = {
            "type": "object",
            "properties": {"name": {"type": "string"}, "age": {"type": "integer"}},
            "required": ["name"],
        }

        data = {"name": "张三", "age": 25}

        errors = SchemaValidator.validate_data_against_schema(data, schema)
        assert len(errors) == 0

    def test_validate_data_against_schema_invalid(self):
        """测试数据不符合Schema"""
        schema = {
            "type": "object",
            "properties": {"name": {"type": "string"}, "age": {"type": "integer"}},
            "required": ["name"],
        }

        # 缺少必需字段
        data = {"age": 25}

        errors = SchemaValidator.validate_data_against_schema(data, schema)
        assert len(errors) > 0
