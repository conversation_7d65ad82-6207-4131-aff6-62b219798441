"""测试新功能"""

import pytest
from unittest.mock import AsyncMock

from memect_insight_extractor import DocumentExtractor, ExtractionConfig
from memect_insight_extractor.models.extraction_request import ExtractionRequest
from memect_insight_extractor.models.schema_models import <PERSON><PERSON><PERSON><PERSON>ield, ExtractionSchema
from memect_insight_extractor.core.prompt_builder import PromptBuilder


class TestNewFeatures:
    """测试新功能"""

    def test_config_debug_mode(self):
        """测试DEBUG模式配置"""
        config = ExtractionConfig(
            llm_api_key="test-key",
            debug_mode=True,
            log_level="DEBUG",
            log_file="test.log"
        )
        
        assert config.debug_mode is True
        assert config.log_level == "DEBUG"
        assert config.log_file == "test.log"

    def test_extraction_request_new_fields(self):
        """测试提取请求的新字段"""
        request = ExtractionRequest(
            text_content="测试文档内容",
            schema={"type": "object", "properties": {"name": {"type": "string"}}},
            extraction_purpose_id="contract_analysis_v1",
            system_prompt="你是一个专业的合同分析专家。请仔细分析文档内容。",
            user_prompt="请提取合同中的关键信息，包括 {field_names}。"
        )
        
        assert request.extraction_purpose_id == "contract_analysis_v1"
        assert request.system_prompt == "你是一个专业的合同分析专家。请仔细分析文档内容。"
        assert request.user_prompt == "请提取合同中的关键信息，包括 {field_names}。"

    def test_schema_field_primary_key(self):
        """测试Schema字段主键支持"""
        field = SchemaField(
            name="contract_id",
            type="string",
            description="合同ID",
            required=True,
            is_primary_key=True
        )
        
        assert field.is_primary_key is True
        assert field.required is True

    def test_extraction_schema_primary_keys(self):
        """测试ExtractionSchema主键方法"""
        fields = [
            SchemaField(name="id", type="string", is_primary_key=True),
            SchemaField(name="name", type="string", required=True),
            SchemaField(name="amount", type="number"),
        ]
        
        schema = ExtractionSchema(
            name="test_schema",
            fields=fields,
            json_schema={"type": "object"}
        )
        
        primary_keys = schema.get_primary_key_fields()
        assert primary_keys == ["id"]

    def test_prompt_builder_variable_replacement(self):
        """测试提示词变量替换"""
        builder = PromptBuilder()
        
        # 测试变量准备
        request = ExtractionRequest(
            text_content="测试文档",
            schema={
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "amount": {"type": "number"}
                }
            },
            document_id="doc_001",
            extraction_purpose_id="test_purpose"
        )
        
        variables = builder._prepare_variables(request, "测试文档")
        
        assert variables["text_content"] == "测试文档"
        assert variables["document_id"] == "doc_001"
        assert variables["extraction_purpose_id"] == "test_purpose"
        assert variables["field_names"] == "name, amount"
        assert variables["field_count"] == "2"

    def test_variable_replacement_formats(self):
        """测试变量替换格式"""
        builder = PromptBuilder()
        
        variables = {
            "field_names": "name, amount",
            "document_id": "doc_001"
        }
        
        # 测试 ${variable} 格式
        text1 = "请提取以下字段: ${field_names}"
        result1 = builder._replace_variables(text1, variables)
        assert result1 == "请提取以下字段: name, amount"
        
        # 测试 {variable} 格式
        text2 = "文档ID: {document_id}"
        result2 = builder._replace_variables(text2, variables)
        assert result2 == "文档ID: doc_001"
        
        # 测试混合格式
        text3 = "文档 {document_id} 包含字段: ${field_names}"
        result3 = builder._replace_variables(text3, variables)
        assert result3 == "文档 doc_001 包含字段: name, amount"

    def test_prompt_builder_with_system_and_user_prompts(self):
        """测试系统提示词和用户提示词构建"""
        builder = PromptBuilder()
        
        request = ExtractionRequest(
            text_content="合同内容...",
            schema={
                "type": "object",
                "properties": {
                    "contract_id": {"type": "string"},
                    "amount": {"type": "number"}
                }
            },
            system_prompt="你是专业的合同分析专家。",
            user_prompt="请提取合同中的 {field_names} 信息。",
            custom_instructions="注意金额格式标准化"
        )
        
        prompt = builder.build_prompt(request)
        
        # 验证包含系统提示词
        assert "你是专业的合同分析专家。" in prompt
        
        # 验证包含用户提示词且变量已替换
        assert "请提取合同中的 contract_id, amount 信息。" in prompt
        
        # 验证包含自定义指令
        assert "注意金额格式标准化" in prompt
        
        # 验证包含文档内容
        assert "合同内容..." in prompt

    def test_extractor_with_debug_mode(self):
        """测试提取器DEBUG模式"""
        config = ExtractionConfig(
            llm_api_key="test-key",
            debug_mode=True,
            log_level="DEBUG"
        )

        # 这里只测试配置，不实际调用LLM
        extractor = DocumentExtractor(config)
        assert extractor.config.debug_mode is True
        assert extractor.config.log_level == "DEBUG"

    def test_backward_compatibility(self):
        """测试向后兼容性"""
        # 测试旧的prompt_template仍然工作
        request = ExtractionRequest(
            text_content="测试内容",
            schema={"type": "object"},
            prompt_template="旧版本的提示词模板"
        )
        
        builder = PromptBuilder()
        variables = builder._prepare_variables(request, "测试内容")
        system_instruction = builder._build_system_instruction(request, variables)
        
        assert system_instruction == "旧版本的提示词模板"
        
        # 测试新的system_prompt优先级更高
        request_new = ExtractionRequest(
            text_content="测试内容",
            schema={"type": "object"},
            system_prompt="新版本的系统提示词",
            prompt_template="旧版本的提示词模板"
        )
        
        system_instruction_new = builder._build_system_instruction(request_new, variables)
        assert system_instruction_new == "新版本的系统提示词"
